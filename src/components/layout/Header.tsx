'use client';

import Link from 'next/link';
import { useEffect, useState } from 'react';

interface HeaderProps {
  lang: string;
}

const Header = ({ lang }: HeaderProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  // Render a static version during SSR
  if (!mounted) {
    return (
      <header className="bg-white shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <img
              src="/vexita_it_logo_cropped.png"
              alt="Vexita IT Logo"
              width={300}
              height={120}
              className="h-20 w-auto"
            />
          </div>

          <nav className="flex items-center space-x-6">
            <span className="text-gray-600">Home</span>
            <span className="text-gray-600">Services</span>
            <span className="text-gray-600">Careers</span>
            <span className="text-gray-600">About</span>
            <span className="text-gray-600">Contact</span>

            <div className="flex items-center space-x-2 ml-4">
              <span className={`px-2 py-1 rounded ${
                lang === "en" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}>
                EN
              </span>
              <span className={`px-2 py-1 rounded ${
                lang === "sv" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}>
                SV
              </span>
            </div>
          </nav>
        </div>
      </header>
    );
  }

  // Render the interactive version after mounting
  return (
    <header className="bg-white shadow-md">
      <div className="container mx-auto flex justify-between items-center">
        <Link href={`/${lang}`} className="flex items-center">
          <img
            src="/vexita_it_logo_cropped.png"
            alt="Vexita IT Logo"
            width={300}
            height={120}
            className="h-20 w-auto"
          />
        </Link>

        <nav className="flex items-center space-x-6">
          <Link href={`/${lang}`} className="text-gray-600 hover:text-gray-900">
            Home
          </Link>
          <Link href={`/${lang}/services`} className="text-gray-600 hover:text-gray-900">
            Services
          </Link>
          <Link href={`/${lang}/careers`} className="text-gray-600 hover:text-gray-900">
            Careers
          </Link>
          <Link href={`/${lang}/about`} className="text-gray-600 hover:text-gray-900">
            About
          </Link>
          <Link href={`/${lang}/contact`} className="text-gray-600 hover:text-gray-900">
            Contact
          </Link>

          <div className="flex items-center space-x-2 ml-4">
            <Link
              href={`/en`}
              className={`px-2 py-1 rounded ${
                lang === "en" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}
            >
              EN
            </Link>
            <Link
              href={`/sv`}
              className={`px-2 py-1 rounded ${
                lang === "sv" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}
            >
              SV
            </Link>
          </div>
        </nav>
      </div>
    </header>
  );
};

export default Header;
