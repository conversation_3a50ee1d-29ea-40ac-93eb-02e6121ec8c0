'use client';

import { useEffect, useState } from 'react';
import Header from './Header';

interface HeaderWrapperProps {
  lang: string;
}

const HeaderWrapper = ({ lang }: HeaderWrapperProps) => {
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    // Return a static version during SSR to prevent hydration mismatch
    return (
      <header className="bg-white shadow-md">
        <div className="container mx-auto flex justify-between items-center">
          <div className="flex items-center">
            <img
              src="/vexita_it_logo_cropped.png"
              alt="Vexita IT Logo"
              width={300}
              height={120}
              className="h-20 w-auto"
            />
          </div>

          <nav className="flex items-center space-x-6">
            <span className="text-gray-600">Home</span>
            <span className="text-gray-600">Services</span>
            <span className="text-gray-600">Careers</span>
            <span className="text-gray-600">About</span>
            <span className="text-gray-600">Contact</span>

            <div className="flex items-center space-x-2 ml-4">
              <span className={`px-2 py-1 rounded ${
                lang === "en" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}>
                EN
              </span>
              <span className={`px-2 py-1 rounded ${
                lang === "sv" ? "bg-gray-200" : "hover:bg-gray-100"
              }`}>
                SV
              </span>
            </div>
          </nav>
        </div>
      </header>
    );
  }

  return <Header lang={lang} />;
};

export default HeaderWrapper;
